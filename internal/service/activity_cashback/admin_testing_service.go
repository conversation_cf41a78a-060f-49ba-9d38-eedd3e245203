package activity_cashback

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
)

// AdminTestingService provides testing utilities for consecutive checkin tasks
type AdminTestingService struct {
	taskManagementService TaskManagementServiceInterface
	progressService       TaskProgressServiceInterface
}

// NewAdminTestingService creates a new admin testing service
func NewAdminTestingService(
	taskManagementService TaskManagementServiceInterface,
	progressService TaskProgressServiceInterface,
) *AdminTestingService {
	return &AdminTestingService{
		taskManagementService: taskManagementService,
		progressService:       progressService,
	}
}

// SetConsecutiveCheckinStreak sets the streak count for consecutive checkin tasks
func (s *AdminTestingService) SetConsecutiveCheckinStreak(ctx context.Context, userID uuid.UUID, streakCount int) error {
	if global.GVA_CONFIG.System.Env == "production" {
		return fmt.Errorf("admin testing functions are not allowed in production")
	}

	// Get all consecutive checkin tasks
	tasks, err := s.taskManagementService.GetTasksByCategory(ctx, model.CategoryDaily)
	if err != nil {
		return fmt.Errorf("failed to get daily tasks: %w", err)
	}

	consecutiveTaskIdentifiers := []model.TaskIdentifier{
		model.TaskIDConsecutiveCheckin3,
		model.TaskIDConsecutiveCheckin7,
		model.TaskIDConsecutiveCheckin30,
	}

	for _, task := range tasks {
		if task.TaskIdentifier == nil {
			continue
		}

		// Check if this is a consecutive checkin task
		isConsecutiveTask := false
		for _, identifier := range consecutiveTaskIdentifiers {
			if *task.TaskIdentifier == identifier {
				isConsecutiveTask = true
				break
			}
		}

		if !isConsecutiveTask {
			continue
		}

		// Set the streak count manually
		if err := s.setStreakDirectly(ctx, userID, task.ID, streakCount); err != nil {
			global.GVA_LOG.Error("Failed to set streak for consecutive checkin task",
				zap.String("task_identifier", string(*task.TaskIdentifier)),
				zap.Error(err))
			continue
		}

		global.GVA_LOG.Info("Set streak for consecutive checkin task",
			zap.String("user_id", userID.String()),
			zap.String("task_identifier", string(*task.TaskIdentifier)),
			zap.Int("streak_count", streakCount))
	}

	return nil
}

// setStreakDirectly sets streak count without validation
func (s *AdminTestingService) setStreakDirectly(ctx context.Context, userID, taskID uuid.UUID, streakCount int) error {
	progress, err := s.progressService.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		progress, err = s.progressService.InitializeTaskProgress(ctx, userID, taskID)
		if err != nil {
			return err
		}
	}

	// Set streak count directly
	progress.StreakCount = streakCount
	progress.ProgressValue = streakCount

	// Set last completed at to yesterday
	yesterday := time.Now().Add(-24 * time.Hour)
	progress.LastCompletedAt = &yesterday
	progress.UpdatedAt = time.Now()

	if streakCount > 0 {
		progress.Status = model.TaskStatusCompleted
	} else {
		progress.Status = model.TaskStatusPending
	}

	return nil
}
