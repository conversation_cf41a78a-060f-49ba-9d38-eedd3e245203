package activity_cashback

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// MockTimeProvider allows us to control time in tests
type MockTimeProvider struct {
	currentTime time.Time
}

func NewMockTimeProvider(startTime time.Time) *MockTimeProvider {
	return &MockTimeProvider{currentTime: startTime}
}

func (m *MockTimeProvider) Now() time.Time {
	return m.currentTime
}

func (m *MockTimeProvider) AdvanceDays(days int) {
	m.currentTime = m.currentTime.AddDate(0, 0, days)
}

// TestConsecutiveCheckinFastFlow tests the complete consecutive checkin flow without waiting
func TestConsecutiveCheckinFastFlow(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping fast flow test in short mode")
	}

	ctx := context.Background()
	userID := uuid.New()

	// Initialize service (you'll need to adjust this based on your test setup)
	service := NewActivityCashbackService() // Adjust based on your constructor

	// Mock time provider starting from a specific date
	mockTime := NewMockTimeProvider(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC))

	t.Run("Complete_3_7_30_Day_Flow", func(t *testing.T) {
		// Day 1: First checkin
		t.Logf("Day 1: First checkin")
		err := simulateCheckinAtTime(ctx, service, userID, mockTime.Now())
		require.NoError(t, err)

		status := getConsecutiveStatus(ctx, service, userID)
		assert.Equal(t, 1, status.streakCount)
		assert.Equal(t, model.TaskIDConsecutiveCheckin3, status.visibleTask)
		assert.Equal(t, 0, status.completedMilestones)

		// Day 2: Second checkin
		mockTime.AdvanceDays(1)
		t.Logf("Day 2: Second checkin")
		err = simulateCheckinAtTime(ctx, service, userID, mockTime.Now())
		require.NoError(t, err)

		status = getConsecutiveStatus(ctx, service, userID)
		assert.Equal(t, 2, status.streakCount)
		assert.Equal(t, model.TaskIDConsecutiveCheckin3, status.visibleTask)
		assert.Equal(t, 0, status.completedMilestones)

		// Day 3: Complete 3-day milestone
		mockTime.AdvanceDays(1)
		t.Logf("Day 3: Complete 3-day milestone")
		err = simulateCheckinAtTime(ctx, service, userID, mockTime.Now())
		require.NoError(t, err)

		status = getConsecutiveStatus(ctx, service, userID)
		assert.Equal(t, 3, status.streakCount)
		assert.Equal(t, model.TaskIDConsecutiveCheckin7, status.visibleTask) // Should show 7-day task now
		assert.Equal(t, 1, status.completedMilestones)                       // 3-day milestone completed
		assert.Equal(t, 50, status.pointsEarned)                             // 50 points from 3-day milestone

		// Days 4-6: Continue towards 7-day milestone
		for day := 4; day <= 6; day++ {
			mockTime.AdvanceDays(1)
			t.Logf("Day %d: Continue towards 7-day milestone", day)
			err = simulateCheckinAtTime(ctx, service, userID, mockTime.Now())
			require.NoError(t, err)

			status = getConsecutiveStatus(ctx, service, userID)
			assert.Equal(t, day, status.streakCount)
			assert.Equal(t, model.TaskIDConsecutiveCheckin7, status.visibleTask)
		}

		// Day 7: Complete 7-day milestone
		mockTime.AdvanceDays(1)
		t.Logf("Day 7: Complete 7-day milestone")
		err = simulateCheckinAtTime(ctx, service, userID, mockTime.Now())
		require.NoError(t, err)

		status = getConsecutiveStatus(ctx, service, userID)
		assert.Equal(t, 7, status.streakCount)
		assert.Equal(t, model.TaskIDConsecutiveCheckin30, status.visibleTask) // Should show 30-day task now
		assert.Equal(t, 2, status.completedMilestones)                        // Both 3-day and 7-day completed
		assert.Equal(t, 250, status.pointsEarned)                             // 50 + 200 points

		// Days 8-29: Continue towards 30-day milestone (simulate quickly)
		for day := 8; day <= 29; day++ {
			mockTime.AdvanceDays(1)
			if day%5 == 0 { // Log every 5th day
				t.Logf("Day %d: Continue towards 30-day milestone", day)
			}
			err = simulateCheckinAtTime(ctx, service, userID, mockTime.Now())
			require.NoError(t, err)

			if day%10 == 0 { // Check status every 10th day
				status = getConsecutiveStatus(ctx, service, userID)
				assert.Equal(t, day, status.streakCount)
				assert.Equal(t, model.TaskIDConsecutiveCheckin30, status.visibleTask)
			}
		}

		// Day 30: Complete 30-day milestone
		mockTime.AdvanceDays(1)
		t.Logf("Day 30: Complete 30-day milestone")
		err = simulateCheckinAtTime(ctx, service, userID, mockTime.Now())
		require.NoError(t, err)

		status = getConsecutiveStatus(ctx, service, userID)
		assert.Equal(t, 0, status.streakCount)                               // Should reset to 0 after 30-day completion
		assert.Equal(t, model.TaskIDConsecutiveCheckin3, status.visibleTask) // Should show 3-day task again
		assert.Equal(t, 3, status.completedMilestones)                       // All milestones completed
		assert.Equal(t, 1250, status.pointsEarned)                           // 50 + 200 + 1000 points

		t.Logf("Successfully completed full 30-day consecutive checkin cycle!")
	})

	t.Run("Test_Streak_Break", func(t *testing.T) {
		// Reset for new test
		userID = uuid.New()
		mockTime = NewMockTimeProvider(time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC))

		// Build up a 5-day streak
		for day := 1; day <= 5; day++ {
			if day > 1 {
				mockTime.AdvanceDays(1)
			}
			err := simulateCheckinAtTime(ctx, service, userID, mockTime.Now())
			require.NoError(t, err)
		}

		status := getConsecutiveStatus(ctx, service, userID)
		assert.Equal(t, 5, status.streakCount)
		assert.Equal(t, model.TaskIDConsecutiveCheckin7, status.visibleTask)

		// Skip a day (break the streak)
		mockTime.AdvanceDays(2) // Skip day 6, now on day 7
		t.Logf("Day 7: Checkin after skipping day 6 (streak should reset)")
		err := simulateCheckinAtTime(ctx, service, userID, mockTime.Now())
		require.NoError(t, err)

		status = getConsecutiveStatus(ctx, service, userID)
		assert.Equal(t, 1, status.streakCount)                               // Should reset to 1
		assert.Equal(t, model.TaskIDConsecutiveCheckin3, status.visibleTask) // Should show 3-day task again

		t.Logf("Successfully tested streak break and reset!")
	})
}

// ConsecutiveStatus holds the current status of consecutive checkin tasks
type ConsecutiveStatus struct {
	streakCount         int
	visibleTask         model.TaskIdentifier
	completedMilestones int
	pointsEarned        int
}

// simulateCheckinAtTime simulates a checkin at a specific time
func simulateCheckinAtTime(ctx context.Context, service ActivityCashbackServiceInterface, userID uuid.UUID, checkTime time.Time) error {
	// This would need to be implemented based on your service interface
	// For now, this is a placeholder that shows the concept

	// You would typically:
	// 1. Find the daily checkin task
	// 2. Complete it with the specific time
	// 3. This should trigger the consecutive checkin logic

	// Example implementation (adjust based on your actual service):
	/*
		tasks, err := service.GetTasksByCategory(ctx, model.CategoryDaily)
		if err != nil {
			return err
		}

		for _, task := range tasks {
			if task.TaskIdentifier != nil && *task.TaskIdentifier == model.TaskIDDailyCheckin {
				// Simulate completing daily checkin at specific time
				return service.CompleteTaskWithPoints(ctx, userID, task.ID, map[string]interface{}{
					"method": "test_simulation",
					"time": checkTime,
				})
			}
		}
	*/

	return nil // Placeholder
}

// getConsecutiveStatus gets the current consecutive checkin status
func getConsecutiveStatus(ctx context.Context, service ActivityCashbackServiceInterface, userID uuid.UUID) ConsecutiveStatus {
	// This would need to be implemented based on your service interface
	// For now, this is a placeholder that shows the concept

	status := ConsecutiveStatus{}

	// You would typically:
	// 1. Get all consecutive checkin tasks
	// 2. Check their progress
	// 3. Determine which task is visible
	// 4. Count completed milestones and total points

	// Example implementation (adjust based on your actual service):
	/*
		tasks, err := service.GetTasksByCategory(ctx, model.CategoryDaily)
		if err != nil {
			return status
		}

		maxStreak := 0
		totalPoints := 0
		completedMilestones := 0

		for _, task := range tasks {
			if task.TaskIdentifier != nil {
				switch *task.TaskIdentifier {
				case model.TaskIDConsecutiveCheckin3, model.TaskIDConsecutiveCheckin7, model.TaskIDConsecutiveCheckin30:
					progress, err := service.GetTaskProgress(ctx, userID, task.ID)
					if err == nil && progress != nil {
						if progress.StreakCount > maxStreak {
							maxStreak = progress.StreakCount
						}
						totalPoints += progress.PointsEarned
						if progress.CompletionCount > 0 {
							completedMilestones++
						}
					}
				}
			}
		}

		status.streakCount = maxStreak
		status.pointsEarned = totalPoints
		status.completedMilestones = completedMilestones

		// Determine visible task based on streak
		if maxStreak < 3 {
			status.visibleTask = model.TaskIDConsecutiveCheckin3
		} else if maxStreak >= 3 && maxStreak < 7 {
			status.visibleTask = model.TaskIDConsecutiveCheckin7
		} else if maxStreak >= 7 && maxStreak < 30 {
			status.visibleTask = model.TaskIDConsecutiveCheckin30
		} else {
			status.visibleTask = model.TaskIDConsecutiveCheckin3 // Reset after 30 days
		}
	*/

	return status
}

// BenchmarkConsecutiveCheckinFlow benchmarks the consecutive checkin flow
func BenchmarkConsecutiveCheckinFlow(b *testing.B) {
	ctx := context.Background()
	service := NewActivityCashbackService() // Adjust based on your constructor

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		userID := uuid.New()
		mockTime := NewMockTimeProvider(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC))

		// Simulate 30 days of checkins
		for day := 1; day <= 30; day++ {
			if day > 1 {
				mockTime.AdvanceDays(1)
			}
			_ = simulateCheckinAtTime(ctx, service, userID, mockTime.Now())
		}
	}
}
