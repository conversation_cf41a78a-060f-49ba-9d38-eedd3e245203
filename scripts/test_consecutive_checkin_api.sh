#!/bin/bash

# <PERSON>ript để test consecutive checkin tasks thông qua API
# Sử dụng trong development environment

set -e

# Configuration
BASE_URL="http://localhost:8080"
GRAPHQL_ENDPOINT="${BASE_URL}/graphql"
ADMIN_API_KEY="local-internal-api-key-for-development-only"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to generate JWT token for testing
generate_test_token() {
    local user_email="${1:-<EMAIL>}"

    log_info "Generating JWT token for user: $user_email"

    # Sử dụng JWT generator script
    if [ -f "./scripts/generate-jwt.sh" ]; then
        TOKEN=$(./scripts/generate-jwt.sh --email "$user_email" --env local --quiet)
        if [ $? -eq 0 ]; then
            echo "$TOKEN"
        else
            log_error "Failed to generate JWT token"
            return 1
        fi
    else
        log_error "JWT generator script not found"
        return 1
    fi
}

# Function to make GraphQL query
graphql_query() {
    local query="$1"
    local variables="$2"
    local token="$3"

    local headers="-H 'Content-Type: application/json'"
    if [ -n "$token" ]; then
        headers="$headers -H 'Authorization: Bearer $token'"
    fi

    local data="{\"query\": \"$query\""
    if [ -n "$variables" ]; then
        data="$data, \"variables\": $variables"
    fi
    data="$data}"

    curl -s -X POST \
        $headers \
        -d "$data" \
        "$GRAPHQL_ENDPOINT"
}

# Function to get user's current consecutive checkin status
get_consecutive_status() {
    local token="$1"

    log_info "Getting current consecutive checkin status..."

    local query='
    query {
        taskCenter {
            categories {
                category {
                    name
                }
                tasks {
                    task {
                        id
                        taskIdentifier
                        name
                        points
                    }
                    progress {
                        streakCount
                        progressValue
                        status
                        lastCompletedAt
                        pointsEarned
                    }
                }
            }
        }
    }'

    local response=$(graphql_query "$query" "" "$token")
    echo "$response" | jq '.data.taskCenter.categories[] | select(.category.name == "daily") | .tasks[] | select(.task.taskIdentifier | contains("CONSECUTIVE_CHECKIN"))'
}

# Function to complete daily checkin (which triggers consecutive checkin logic)
complete_daily_checkin() {
    local token="$1"

    log_info "Completing daily checkin to trigger consecutive checkin logic..."

    # First get daily checkin task ID
    local query='
    query {
        taskCenter {
            categories {
                category {
                    name
                }
                tasks {
                    task {
                        id
                        taskIdentifier
                        name
                    }
                }
            }
        }
    }'

    local response=$(graphql_query "$query" "" "$token")
    local daily_checkin_id=$(echo "$response" | jq -r '.data.taskCenter.categories[] | select(.category.name == "daily") | .tasks[] | select(.task.taskIdentifier == "DAILY_CHECKIN") | .task.id')

    if [ "$daily_checkin_id" = "null" ] || [ -z "$daily_checkin_id" ]; then
        log_error "Could not find daily checkin task ID"
        return 1
    fi

    log_info "Found daily checkin task ID: $daily_checkin_id"

    # Complete the daily checkin task
    local mutation='
    mutation($taskId: ID!) {
        completeTask(input: {
            taskId: $taskId,
            verificationData: "{\"method\": \"api_test\"}"
        }) {
            success
            pointsAwarded
            message
        }
    }'

    local variables="{\"taskId\": \"$daily_checkin_id\"}"
    local response=$(graphql_query "$mutation" "$variables" "$token")

    local success=$(echo "$response" | jq -r '.data.completeTask.success')
    local points=$(echo "$response" | jq -r '.data.completeTask.pointsAwarded')
    local message=$(echo "$response" | jq -r '.data.completeTask.message')

    if [ "$success" = "true" ]; then
        log_success "Daily checkin completed successfully! Points awarded: $points"
        if [ "$message" != "null" ]; then
            log_info "Message: $message"
        fi
    else
        log_warning "Daily checkin response: $response"
    fi
}

# Function to simulate consecutive checkin test scenarios
test_consecutive_scenarios() {
    local user_email="${1:-<EMAIL>}"

    log_info "Starting consecutive checkin test scenarios for user: $user_email"

    # Generate token
    local token=$(generate_test_token "$user_email")
    if [ $? -ne 0 ]; then
        log_error "Failed to generate token"
        return 1
    fi

    log_success "Generated token successfully"

    echo "========================================="
    echo "SCENARIO 1: Check initial status"
    echo "========================================="

    get_consecutive_status "$token"

    echo ""
    echo "========================================="
    echo "SCENARIO 2: Complete daily checkin"
    echo "========================================="

    complete_daily_checkin "$token"

    echo ""
    echo "========================================="
    echo "SCENARIO 3: Check status after checkin"
    echo "========================================="

    get_consecutive_status "$token"

    echo ""
    log_success "Test scenarios completed!"
}

# Main function
main() {
    local user_email="$1"

    echo "========================================="
    echo "Consecutive Checkin API Test Script"
    echo "========================================="

    # Check if server is running
    log_info "Checking if server is running..."
    if ! curl -s "$BASE_URL/health" > /dev/null; then
        log_error "Server is not running at $BASE_URL"
        log_info "Please start the server first: make dev"
        exit 1
    fi

    log_success "Server is running"

    # Check if jq is installed
    if ! command -v jq &> /dev/null; then
        log_error "jq is required but not installed. Please install jq first."
        log_info "On macOS: brew install jq"
        log_info "On Ubuntu: sudo apt-get install jq"
        exit 1
    fi

    # Run test scenarios
    test_consecutive_scenarios "$user_email"

    echo ""
    echo "========================================="
    echo "Manual Testing Instructions"
    echo "========================================="
    echo "1. Use the database script to set specific streak counts:"
    echo "   psql -d your_db -f scripts/test_consecutive_checkin_manual.sql"
    echo ""
    echo "2. Or use the admin API (if implemented):"
    echo "   curl -X POST $BASE_URL/api/admin/testing/set-streak \\"
    echo "        -H 'X-Admin-Token: $ADMIN_API_KEY' \\"
    echo "        -d '{\"user_id\": \"uuid\", \"streak_count\": 2}'"
    echo ""
    echo "3. Then run this script again to see the effects"
    echo "========================================="
}

# Help function
show_help() {
    echo "Usage: $0 [USER_EMAIL]"
    echo ""
    echo "Test consecutive checkin tasks through API calls"
    echo ""
    echo "Arguments:"
    echo "  USER_EMAIL    Email of test user (default: <EMAIL>)"
    echo ""
    echo "Examples:"
    echo "  $0                          # Test with default user"
    echo "  $0 <EMAIL>       # Test with specific user"
    echo ""
    echo "Prerequisites:"
    echo "  - Server must be running (make dev)"
    echo "  - jq must be installed"
    echo "  - User must exist in database"
}

# Parse arguments
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# Run main function
main "$1"
