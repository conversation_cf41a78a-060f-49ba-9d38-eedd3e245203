package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/config"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initialize"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
)

func main() {
	// Command line flags
	var (
		userEmail   = flag.String("user", "<EMAIL>", "User email to test with")
		streakCount = flag.Int("streak", 0, "Set specific streak count (0-30)")
		action      = flag.String("action", "status", "Action: status, set-streak, simulate, reset")
		days        = flag.Int("days", 30, "Number of days to simulate (for simulate action)")
		configPath  = flag.String("config", "config.yaml", "Path to config file")
	)
	flag.Parse()

	// Load configuration
	if err := loadConfig(*configPath); err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize database and services
	ctx := context.Background()
	service, cleanup, err := initializeServices(ctx)
	if err != nil {
		log.Fatalf("Failed to initialize services: %v", err)
	}
	defer cleanup()

	// Get or create user
	userID, err := getUserID(ctx, service, *userEmail)
	if err != nil {
		log.Fatalf("Failed to get user ID: %v", err)
	}

	fmt.Printf("🎯 Consecutive Checkin Tester\n")
	fmt.Printf("User: %s (%s)\n", *userEmail, userID.String())
	fmt.Printf("Action: %s\n\n", *action)

	// Execute action
	switch *action {
	case "status":
		if err := showStatus(ctx, service, userID); err != nil {
			log.Fatalf("Failed to show status: %v", err)
		}
	case "set-streak":
		if err := setStreak(ctx, service, userID, *streakCount); err != nil {
			log.Fatalf("Failed to set streak: %v", err)
		}
	case "simulate":
		if err := simulateFlow(ctx, service, userID, *days); err != nil {
			log.Fatalf("Failed to simulate flow: %v", err)
		}
	case "reset":
		if err := resetStreak(ctx, service, userID); err != nil {
			log.Fatalf("Failed to reset streak: %v", err)
		}
	default:
		fmt.Printf("❌ Unknown action: %s\n", *action)
		fmt.Println("Available actions: status, set-streak, simulate, reset")
		os.Exit(1)
	}
}

func loadConfig(configPath string) error {
	// Load configuration (adjust based on your config loading logic)
	if err := config.LoadConfig(configPath); err != nil {
		return fmt.Errorf("failed to load config from %s: %w", configPath, err)
	}
	return nil
}

func initializeServices(ctx context.Context) (*activity_cashback.ActivityCashbackService, func(), error) {
	// Initialize database connection and services
	// This is a simplified version - adjust based on your initialization logic

	if err := initialize.InitializeDatabase(); err != nil {
		return nil, nil, fmt.Errorf("failed to initialize database: %w", err)
	}

	service := activity_cashback.NewActivityCashbackService()

	cleanup := func() {
		// Add cleanup logic if needed
		if global.GVA_DB != nil {
			sqlDB, _ := global.GVA_DB.DB()
			if sqlDB != nil {
				sqlDB.Close()
			}
		}
	}

	return service, cleanup, nil
}

func getUserID(ctx context.Context, service *activity_cashback.ActivityCashbackService, email string) (uuid.UUID, error) {
	// This is a placeholder - you'll need to implement user lookup based on your user system
	// For testing, you might want to create a test user if it doesn't exist

	// For now, generate a deterministic UUID based on email
	// In a real implementation, you'd query your user database
	return uuid.NewSHA1(uuid.NameSpaceDNS, []byte(email)), nil
}

func showStatus(ctx context.Context, service *activity_cashback.ActivityCashbackService, userID uuid.UUID) error {
	fmt.Println("📊 Current Consecutive Checkin Status")
	fmt.Println("=====================================")

	// Get task center to see current status
	taskCenter, err := service.GetTaskCenter(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get task center: %w", err)
	}

	// Find consecutive checkin tasks
	found := false
	for _, category := range taskCenter.Categories {
		if category.Category.Name != "daily" {
			continue
		}

		for _, taskWithProgress := range category.Tasks {
			task := taskWithProgress.Task
			if task.TaskIdentifier == nil {
				continue
			}

			switch *task.TaskIdentifier {
			case "CONSECUTIVE_CHECKIN_3", "CONSECUTIVE_CHECKIN_7", "CONSECUTIVE_CHECKIN_30":
				found = true
				fmt.Printf("📅 %s\n", task.Name)
				fmt.Printf("   ID: %s\n", task.ID.String())
				fmt.Printf("   Points: %d\n", task.Points)

				if taskWithProgress.Progress != nil {
					progress := taskWithProgress.Progress
					fmt.Printf("   Streak: %d days\n", progress.StreakCount)
					fmt.Printf("   Progress: %d\n", progress.ProgressValue)
					fmt.Printf("   Status: %s\n", progress.Status)
					fmt.Printf("   Points Earned: %d\n", progress.PointsEarned)
					if progress.LastCompletedAt != nil {
						fmt.Printf("   Last Completed: %s\n", progress.LastCompletedAt.Format("2006-01-02 15:04:05"))
					}
				} else {
					fmt.Printf("   Status: Not started\n")
				}
				fmt.Println()
			}
		}
	}

	if !found {
		fmt.Println("❌ No consecutive checkin tasks found")
	}

	return nil
}

func setStreak(ctx context.Context, service *activity_cashback.ActivityCashbackService, userID uuid.UUID, streakCount int) error {
	fmt.Printf("🔧 Setting streak count to %d days\n", streakCount)

	if streakCount < 0 || streakCount > 30 {
		return fmt.Errorf("invalid streak count: %d (must be 0-30)", streakCount)
	}

	// You would need to implement the admin testing service or direct database manipulation here
	// For now, this is a placeholder showing the concept

	fmt.Printf("⚠️  Direct streak manipulation not implemented in this demo\n")
	fmt.Printf("💡 Use the SQL script instead:\n")
	fmt.Printf("   psql -d your_db -f scripts/test_consecutive_checkin_manual.sql\n")
	fmt.Printf("   (Update the script with user_id: %s and streak_count: %d)\n", userID.String(), streakCount)

	return nil
}

func simulateFlow(ctx context.Context, service *activity_cashback.ActivityCashbackService, userID uuid.UUID, days int) error {
	fmt.Printf("🎬 Simulating %d days of consecutive checkins\n", days)
	fmt.Println("==========================================")

	if days <= 0 || days > 365 {
		return fmt.Errorf("invalid days count: %d (must be 1-365)", days)
	}

	// Show initial status
	fmt.Println("📊 Initial Status:")
	if err := showStatus(ctx, service, userID); err != nil {
		return err
	}

	// Simulate daily checkins
	for day := 1; day <= days; day++ {
		fmt.Printf("Day %d: ", day)

		// You would implement the actual checkin simulation here
		// For now, this is a placeholder
		fmt.Printf("Simulated checkin ✓\n")

		// Show milestone achievements
		if day == 3 {
			fmt.Printf("  🎉 3-day milestone reached! (+50 points)\n")
		} else if day == 7 {
			fmt.Printf("  🎉 7-day milestone reached! (+200 points)\n")
		} else if day == 30 {
			fmt.Printf("  🎉 30-day milestone reached! (+1000 points)\n")
			fmt.Printf("  🔄 Streak reset, starting new cycle\n")
		}

		// Add small delay for visual effect
		time.Sleep(100 * time.Millisecond)
	}

	fmt.Println("\n📊 Final Status:")
	return showStatus(ctx, service, userID)
}

func resetStreak(ctx context.Context, service *activity_cashback.ActivityCashbackService, userID uuid.UUID) error {
	fmt.Println("🔄 Resetting all consecutive checkin streaks")

	// You would implement the actual reset logic here
	// For now, this is a placeholder

	fmt.Printf("⚠️  Direct streak reset not implemented in this demo\n")
	fmt.Printf("💡 Use the SQL script or admin API instead\n")

	return nil
}

// Helper function to parse user ID from string
func parseUUID(s string) (uuid.UUID, error) {
	return uuid.Parse(s)
}
